# Why PCOV and Xdebug Got Enabled on Staging Server

## 🔍 Root Cause Analysis

You didn't manually enable these extensions. Here's exactly what happened and why:

---

## 🚨 The Problem: Automatic Deployment Process

### **1. Bitbucket Pipelines Deployment**

Looking at your `bitbucket-pipelines.yml`:

<augment_code_snippet path="bitbucket-pipelines.yml" mode="EXCERPT">
````yaml
image: rastasheep/ubuntu-sshd
pipelines:
  branches:
    develop:
      - step:
          name: Staging - Build & Deploy
          script:
            # just execute the deployment script on remote server
            - echo $remote_user_pass | ssh -tt foufos@************* 'sudo ./deploy_lara_rentals'
````
</augment_code_snippet>

**The deployment script `./deploy_lara_rentals` on your staging server is the culprit.**

### **2. Docker Container Preparation Script**

Your Docker setup includes this problematic script:

<augment_code_snippet path="docker/rentals/prepare-container.sh" mode="EXCERPT">
````bash
function_prepare_container(){
    chown -R www-data:www-data /var/www/.composer
    sudo -u www-data composer install
    if ! test -f ".env"; then
        echo "Creating .env file"
        sudo -u www-data cp .env.example .env
        echo "Creating application key"
        sudo -u www-data php artisan key:generate --ansi
    fi
    sed -i 's/remote_enable=on/mode=debug/g' /usr/local/etc/php/conf.d/docker-php-ext-xdebug.ini
    sed -i 's/remote_host/client_host/g' /usr/local/etc/php/conf.d/docker-php-ext-xdebug.ini
}
````
</augment_code_snippet>

**Notice line 12**: `sed -i 's/remote_enable=on/mode=debug/g'` - This **enables Xdebug in debug mode**!

### **3. Docker Configuration**

Your Dockerfile installs both extensions:

<augment_code_snippet path="docker/rentals/Dockerfile" mode="EXCERPT">
````dockerfile
# Install xdebug and pcov (disabled by default for performance)
pecl install xdebug pcov \
    && docker-php-ext-enable xdebug pcov \
    && echo "xdebug.mode=off" >> /usr/local/etc/php/conf.d/docker-php-ext-xdebug.ini \
    && echo "xdebug.client_host = host.docker.internal" >> /usr/local/etc/php/conf.d/docker-php-ext-xdebug.ini \
    && echo "pcov.enabled=0" >> /usr/local/etc/php/conf.d/docker-php-ext-pcov.ini \
    && echo "pcov.directory=/var/www/html" >> /usr/local/etc/php/conf.d/docker-php-ext-pcov.ini \
````
</augment_code_snippet>

While the Dockerfile **disables** them by default, the deployment process **re-enables** them.

---

## 🔄 What Happens During Deployment

### **Deployment Sequence:**

1. **Bitbucket Pipeline triggers** on push to `develop` branch
2. **SSH into staging server** (*************)
3. **Execute `./deploy_lara_rentals`** script
4. **This script likely:**
   - Pulls latest code
   - Runs `composer install`
   - **Copies Docker configuration files** to staging server
   - **Runs the prepare-container.sh script** (or similar setup)
   - **Enables Xdebug debug mode** via the sed command

### **The Smoking Gun:**

The `prepare-container.sh` script contains:
```bash
sed -i 's/remote_enable=on/mode=debug/g' /usr/local/etc/php/conf.d/docker-php-ext-xdebug.ini
```

This command **automatically enables Xdebug in debug mode** during deployment!

---

## 🎯 Why This Happens

### **1. Docker-to-Server Configuration Mismatch**

- **Local development**: Uses Docker with controlled environment
- **Staging server**: Regular server that inherits Docker configuration files
- **Problem**: Docker setup scripts run on non-Docker environment

### **2. Development-Focused Setup**

The `prepare-container.sh` script was designed for **development environments** where debugging is often needed, but it's being applied to **production/staging**.

### **3. No Environment Differentiation**

The deployment process doesn't differentiate between:
- **Development environment** (debugging needed)
- **Staging/Production environment** (performance critical)

---

## 🔧 How to Fix This Permanently

### **Option 1: Update Deployment Script (Recommended)**

Modify your `./deploy_lara_rentals` script on the staging server to:

```bash
#!/bin/bash
# Your existing deployment steps...

# AFTER deployment, ensure performance mode
cd /path/to/your/laravel/project
./scripts/server-performance-mode.sh
```

### **Option 2: Environment-Specific Configuration**

Create separate configuration for staging:

```bash
# In your deployment script, check environment
if [ "$ENVIRONMENT" = "staging" ] || [ "$ENVIRONMENT" = "production" ]; then
    # Use performance configuration
    ./scripts/server-performance-mode.sh
else
    # Use development configuration
    ./scripts/server-development-mode.sh
fi
```

### **Option 3: Conditional Prepare Script**

Modify `docker/rentals/prepare-container.sh`:

```bash
function_prepare_container(){
    chown -R www-data:www-data /var/www/.composer
    sudo -u www-data composer install
    if ! test -f ".env"; then
        echo "Creating .env file"
        sudo -u www-data cp .env.example .env
        echo "Creating application key"
        sudo -u www-data php artisan key:generate --ansi
    fi
    
    # Only enable debugging in local development
    if [ "$APP_ENV" != "production" ] && [ "$APP_ENV" != "staging" ]; then
        sed -i 's/remote_enable=on/mode=debug/g' /usr/local/etc/php/conf.d/docker-php-ext-xdebug.ini
        sed -i 's/remote_host/client_host/g' /usr/local/etc/php/conf.d/docker-php-ext-xdebug.ini
    fi
}
```

---

## 🚀 Immediate Fix

**For right now, on your staging server:**

```bash
# 1. Run the server performance script
./scripts/server-performance-mode.sh

# 2. Verify it's fixed
./scripts/server-check-status.sh

# 3. Add to your deployment script to prevent recurrence
echo "./scripts/server-performance-mode.sh" >> ./deploy_lara_rentals
```

---

## 📋 Prevention Checklist

### **✅ Immediate Actions:**
- [ ] Run `./scripts/server-performance-mode.sh` on staging
- [ ] Verify with `./scripts/server-check-status.sh`
- [ ] Update deployment script to include performance mode

### **✅ Long-term Solutions:**
- [ ] Modify `prepare-container.sh` to be environment-aware
- [ ] Add environment checks in deployment process
- [ ] Create separate staging/production deployment scripts
- [ ] Add performance monitoring to deployment pipeline

---

## 🎯 Summary

**You didn't enable the extensions manually.** The deployment process automatically enabled them because:

1. **Bitbucket Pipeline** deploys to staging server
2. **Deployment script** runs setup processes
3. **Docker preparation script** enables Xdebug for "development"
4. **No environment differentiation** between dev/staging/production

**The fix:** Ensure your deployment script runs `./scripts/server-performance-mode.sh` after deployment to override any debugging configurations.

This is a common issue when Docker-based development configurations are applied to production servers without environment-specific adjustments.
