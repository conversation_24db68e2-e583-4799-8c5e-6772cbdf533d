# Performance Fix: Debugging Extensions Management

## Problem

The fixes branch was experiencing dramatic performance slowdowns (10+ seconds page load times) due to debugging and coverage extensions being enabled by default.

## Root Cause

Both **Xdebug** (in debug mode) and **PCOV** (code coverage) extensions were enabled simultaneously, causing:
- Significant overhead on every PHP operation
- Memory and CPU intensive debugging hooks
- Code coverage tracking during normal development

## Solution

### 1. Disabled Extensions by Default

Modified `docker/rentals/Dockerfile` to disable debugging extensions by default:
- `xdebug.mode=off` (was `debug`)
- `pcov.enabled=0` (was `1`)

### 2. Performance Management Scripts

Created utility scripts for easy debugging/coverage management:

#### **Performance Mode (Default)**
```bash
./scripts/performance-mode.sh
```
- Disables all debugging extensions
- Optimizes for fast page loads
- Recommended for normal development

#### **Toggle Debugging**
```bash
# Enable Xdebug for debugging
./scripts/toggle-debug.sh on

# Disable Xdebug (back to performance mode)
./scripts/toggle-debug.sh off
```

#### **Toggle Coverage**
```bash
# Enable PCOV for test coverage
./scripts/toggle-coverage.sh on

# Disable PCOV (back to performance mode)
./scripts/toggle-coverage.sh off
```

### 3. Automatic Coverage Management

The test coverage script (`scripts/test-coverage.sh`) now:
- Automatically enables PCOV when running coverage
- Automatically disables PCOV after coverage completes
- Maintains performance when not running tests

## Usage Guidelines

### Normal Development
```bash
# Ensure performance mode is enabled
./scripts/performance-mode.sh

# Your application should now load quickly
```

### When Debugging
```bash
# Enable debugging
./scripts/toggle-debug.sh on

# Debug your application
# ... debugging session ...

# Disable debugging when done
./scripts/toggle-debug.sh off
```

### When Running Tests with Coverage
```bash
# Coverage scripts handle this automatically
composer test-coverage-html

# Or manually
./scripts/toggle-coverage.sh on
composer test
./scripts/toggle-coverage.sh off
```

## Performance Impact

| Mode | Xdebug | PCOV | Page Load Time |
|------|--------|------|----------------|
| **Performance** | Off | Off | ~1-2 seconds |
| **Debug** | On | Off | ~3-5 seconds |
| **Coverage** | Off | On | ~2-4 seconds |
| **Both Enabled** | On | On | ~10+ seconds ❌ |

## Verification

Check current extension status:
```bash
# Check Xdebug mode
docker compose exec rentals php -i | grep "xdebug.mode"

# Check PCOV status
docker compose exec rentals php -i | grep "PCOV support"

# List all debugging extensions
docker compose exec rentals php -m | grep -E "(xdebug|pcov)"
```

## Troubleshooting

### If performance is still slow:
1. Verify extensions are disabled:
   ```bash
   ./scripts/performance-mode.sh
   ```

2. Check for other performance issues:
   - Database queries (use Laravel Debugbar when debugging)
   - Network latency
   - File system performance

### If debugging doesn't work:
1. Ensure Xdebug is enabled:
   ```bash
   ./scripts/toggle-debug.sh on
   ```

2. Configure your IDE for Xdebug connection

### If coverage reports are empty:
1. Ensure PCOV is enabled during tests:
   ```bash
   ./scripts/toggle-coverage.sh on
   composer test
   ```

## Best Practices

1. **Always use performance mode** for normal development
2. **Enable debugging only when needed** and disable when done
3. **Let coverage scripts manage PCOV** automatically
4. **Monitor extension status** if performance degrades
5. **Rebuild containers** after major changes to ensure clean state

## Files Modified

- `docker/rentals/Dockerfile` - Disabled extensions by default
- `scripts/toggle-debug.sh` - Debug mode management
- `scripts/toggle-coverage.sh` - Coverage mode management  
- `scripts/performance-mode.sh` - Performance optimization
- `scripts/test-coverage.sh` - Automatic coverage management
