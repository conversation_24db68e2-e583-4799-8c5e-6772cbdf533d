# Documentation Index

This directory contains comprehensive documentation for performance management, testing, and security.

## 📚 Documentation Overview

### 🚀 Performance Management

#### For Local Development (Docker)
- **[Performance Management Guide](PERFORMANCE_MANAGEMENT_GUIDE.md)** - Complete guide for Docker-based development
  - Performance optimization scripts
  - Debugging tools management
  - Test coverage integration
  - Troubleshooting guide

#### For Staging/Production Servers (Non-Docker)
- **[Server Performance Guide](SERVER_PERFORMANCE_GUIDE.md)** - Complete guide for server environments
  - Server-compatible performance scripts
  - PHP configuration management
  - Web server integration
  - Production best practices

- **[Staging Performance Fix](STAGING_PERFORMANCE_FIX.md)** - Quick fix guide for immediate performance issues
  - Emergency performance fixes
  - Manual configuration steps
  - Troubleshooting checklist

- **[Why Extensions Enabled on Staging](WHY_EXTENSIONS_ENABLED_ON_STAGING.md)** - Root cause analysis
  - Explains automatic deployment issues
  - Bitbucket Pipeline deployment process
  - Prevention strategies

- **[Deployment Process Guide](DEPLOYMENT_PROCESS_GUIDE.md)** - Complete deployment automation
  - Environment-aware deployment process
  - Bitbucket Pipeline integration
  - Server setup and configuration
  - Troubleshooting and monitoring

- **[Implementation Checklist](IMPLEMENTATION_CHECKLIST.md)** - Step-by-step implementation guide
  - Pre-implementation preparation
  - Staging and production setup
  - Verification and testing procedures
  - Troubleshooting checklist

### 🧪 Testing & Coverage

- **[Testing Strategy](TESTING_STRATEGY.md)** - Comprehensive testing documentation
  - Test coverage setup and configuration
  - Coverage reporting (HTML, XML, text)
  - Selective coverage testing
  - CI/CD integration
  - Performance testing

### 🔒 Security

- **[Security Implementation](SECURITY_IMPLEMENTATION.md)** - Security measures and configurations
  - Directory access restrictions
  - File protection rules
  - Production security checklist

---

## 🛠️ Quick Reference

### Local Development (Docker Environment)

```bash
# Performance Management
./scripts/performance-mode.sh       # Default fast mode (recommended)
./scripts/development-mode.sh       # Enable debugging tools
./scripts/check-status.sh           # Check current configuration

# Individual Controls
./scripts/toggle-debug.sh [on|off]     # Xdebug only
./scripts/toggle-coverage.sh [on|off]  # PCOV only

# Testing & Coverage
composer test-coverage-html         # Generate HTML coverage report
./scripts/selective-coverage.sh     # Selective coverage testing
./scripts/test-coverage.sh          # Advanced coverage options

# Maintenance
./scripts/rebuild-container.sh      # Clean rebuild when needed
```

### Staging/Production Servers (Non-Docker)

```bash
# Performance Management
./scripts/server-performance-mode.sh    # Default fast mode (recommended)
./scripts/server-development-mode.sh    # Enable debugging tools
./scripts/server-check-status.sh        # Check current configuration

# Individual Controls
./scripts/server-toggle-debug.sh [on|off]     # Xdebug only
./scripts/server-toggle-coverage.sh [on|off]  # PCOV only
```

---

## 📋 File Organization

### Documentation Files
```
docs/
├── README.md                          # This index file
├── PERFORMANCE_MANAGEMENT_GUIDE.md    # Docker environment performance
├── SERVER_PERFORMANCE_GUIDE.md        # Server environment performance
├── STAGING_PERFORMANCE_FIX.md         # Quick staging fixes
├── TESTING_STRATEGY.md                # Testing and coverage
└── SECURITY_IMPLEMENTATION.md         # Security measures
```

### Script Files
```
scripts/
├── Docker Environment Scripts
│   ├── performance-mode.sh            # Main performance script
│   ├── development-mode.sh            # Debugging mode
│   ├── check-status.sh                # Status monitoring
│   ├── toggle-debug.sh                # Xdebug control
│   ├── toggle-coverage.sh             # PCOV control
│   ├── rebuild-container.sh           # Container rebuild
│   ├── test-coverage.sh               # Coverage testing
│   └── selective-coverage.sh          # Selective coverage
│
└── Server Environment Scripts
    ├── server-performance-mode.sh      # Server performance script
    ├── server-development-mode.sh      # Server debugging mode
    ├── server-check-status.sh          # Server status monitoring
    ├── server-toggle-debug.sh          # Server Xdebug control
    └── server-toggle-coverage.sh       # Server PCOV control
```

---

## 🎯 Getting Started

### For New Developers (Local Development)

1. **Set up performance mode** (recommended default):
   ```bash
   ./scripts/performance-mode.sh
   ```

2. **Verify setup**:
   ```bash
   ./scripts/check-status.sh
   ```

3. **When you need debugging**:
   ```bash
   ./scripts/development-mode.sh
   # ... debug your code ...
   ./scripts/performance-mode.sh  # Return to performance mode
   ```

### For Staging/Production Deployment

1. **Enable performance mode** (critical for production):
   ```bash
   ./scripts/server-performance-mode.sh
   ```

2. **Verify configuration**:
   ```bash
   ./scripts/server-check-status.sh
   ```

3. **Only for troubleshooting** (temporary):
   ```bash
   ./scripts/server-development-mode.sh
   # ... debug the issue ...
   ./scripts/server-performance-mode.sh  # Return to performance mode
   ```

---

## 🚨 Important Notes

### Performance
- **Always use performance mode** as the default state
- **Only enable debugging** when actively troubleshooting
- **Return to performance mode** immediately after debugging

### Environment Separation
- **Docker scripts** (`scripts/*.sh`) for local development
- **Server scripts** (`scripts/server-*.sh`) for staging/production
- **Never mix** Docker and server scripts

### Security
- Scripts and documentation directories are **blocked from web access**
- Configuration files are created with **appropriate permissions**
- No sensitive information is exposed in logs or output

---

For detailed information on any topic, refer to the specific documentation files listed above.
