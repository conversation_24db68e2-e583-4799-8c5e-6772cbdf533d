# 🚀 Staging Server Performance Fix

## Quick Fix for Your Digital Ocean Staging Server

Your staging server is experiencing performance issues because debugging extensions (PCOV/XDEBUG) are likely enabled. Here's how to fix it immediately:

---

## 🎯 Immediate Solution

### Step 1: Upload Server Scripts

Upload these files to your staging server in the `scripts/` directory:
- `scripts/server-performance-mode.sh`
- `scripts/server-development-mode.sh`
- `scripts/server-toggle-debug.sh`
- `scripts/server-toggle-coverage.sh`
- `scripts/server-check-status.sh`

### Step 2: Make Scripts Executable

```bash
chmod +x scripts/server-*.sh
```

### Step 3: Enable Performance Mode

```bash
./scripts/server-performance-mode.sh
```

### Step 4: Verify the Fix

```bash
./scripts/server-check-status.sh
```

**Expected output:**
```
✅ Current Mode: PERFORMANCE MODE (optimal for production)
🟢 Xdebug: DISABLED (performance mode)
🟢 PCOV: DISABLED (performance mode)
🟢 Laravel Debugbar: DISABLED (performance mode)
```

---

## 🔍 Manual Check (Alternative Method)

If you can't use the scripts immediately, you can check and fix manually:

### Check Current Status

```bash
# Check if PCOV is enabled
php -r "echo 'PCOV enabled: ' . ini_get('pcov.enabled') . PHP_EOL;"

# Check if Xdebug is enabled
php -r "echo 'Xdebug mode: ' . ini_get('xdebug.mode') . PHP_EOL;"

# Check Laravel debug mode
grep "APP_DEBUG=" .env
```

### Manual Fix

```bash
# Find PHP config directory
php --ini | grep "Scan for additional .ini files"

# Create performance config files (adjust path as needed)
sudo tee /etc/php/8.1/apache2/conf.d/99-performance.ini > /dev/null << 'EOF'
; Performance mode - disable debugging
xdebug.mode=off
pcov.enabled=0
EOF

# Disable Laravel Debugbar
sed -i 's/APP_DEBUG=true/APP_DEBUG=false/' .env

# Restart web server
sudo systemctl reload apache2
# OR for Nginx + PHP-FPM:
# sudo systemctl reload nginx && sudo systemctl reload php8.1-fpm
```

---

## 🚨 Why This Happens

The performance scripts in your local environment are Docker-based:
```bash
# These work locally (Docker)
./scripts/performance-mode.sh
./scripts/toggle-debug.sh

# But fail on staging because they use:
docker compose exec rentals bash -c "..."
```

Your staging server doesn't have Docker, so these commands fail and debugging extensions remain enabled.

---

## 📊 Expected Performance Improvement

| Before (Debug Mode) | After (Performance Mode) | Improvement |
|-------------------|-------------------------|-------------|
| 10+ seconds | ~2-4 seconds | **60-80% faster** |
| High CPU usage | Normal CPU usage | **Significant reduction** |
| High memory usage | Normal memory usage | **Memory optimization** |

---

## 🔧 Long-term Solution

1. **Always use performance mode on staging/production**
2. **Only enable debugging when actively troubleshooting**
3. **Add performance check to your deployment process**

### Add to Deployment Script

```bash
# Add this to your deployment script
./scripts/server-performance-mode.sh
./scripts/server-check-status.sh
```

---

## 🆘 If You Need Help

If the scripts don't work or you need assistance:

1. **Check PHP configuration:**
   ```bash
   php --ini
   php -m | grep -E "(xdebug|pcov)"
   ```

2. **Check web server:**
   ```bash
   systemctl status apache2
   # OR
   systemctl status nginx
   systemctl status php*-fpm
   ```

3. **Check permissions:**
   ```bash
   ls -la scripts/server-*.sh
   sudo -v  # Test sudo access
   ```

---

## ✅ Success Verification

After applying the fix, you should see:

1. **Faster page loads** - Test with browser or curl
2. **Lower server resource usage** - Check with `top` or `htop`
3. **Performance mode confirmed** - Run `./scripts/server-check-status.sh`

The performance issue should be resolved immediately after applying these changes!
