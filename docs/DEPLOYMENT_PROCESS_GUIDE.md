# Deployment Process Guide

## 🚀 Complete Deployment Process Documentation

This guide provides comprehensive documentation for the automated deployment process with environment-aware performance management.

---

## 📋 Overview

### **Current Deployment Architecture**

```
Local Development → Git Push → Bitbucket Pipeline → Server Deployment
     (Docker)                                           (Non-Docker)
```

### **Servers & Environments**

| Environment | Server IP | Branch | Domain | Purpose |
|-------------|-----------|--------|---------|---------|
| **Staging** | ************* | `develop` | staging.domain.com | Testing & QA |
| **Production** | *************** | `master` | www.domain.com | Live Traffic |
| **Local** | localhost | any | rentals.local | Development |

---

## 🔧 Implementation Steps

### **Phase 1: Server Setup (One-time)**

#### **Step 1.1: Upload New Scripts to Both Servers**

**For Staging Server (*************):**
```bash
# SSH into staging server
ssh foufos@*************

# Navigate to project directory
cd /path/to/your/laravel/project

# Upload these new scripts to scripts/ directory:
# - scripts/detect-environment.sh
# - scripts/auto-performance-mode.sh
# - scripts/setup-environment-flags.sh
# - scripts/prepare-container-environment-aware.sh

# Make scripts executable
chmod +x scripts/detect-environment.sh
chmod +x scripts/auto-performance-mode.sh
chmod +x scripts/setup-environment-flags.sh
chmod +x scripts/prepare-container-environment-aware.sh
```

**For Production Server (***************):**
```bash
# SSH into production server
ssh foufos@***************

# Repeat the same process as staging
cd /path/to/your/laravel/project
# Upload scripts and make executable
chmod +x scripts/detect-environment.sh
chmod +x scripts/auto-performance-mode.sh
chmod +x scripts/setup-environment-flags.sh
chmod +x scripts/prepare-container-environment-aware.sh
```

#### **Step 1.2: Create Environment Flags**

**On Staging Server:**
```bash
# Create staging environment flags
./scripts/setup-environment-flags.sh staging
```

**On Production Server:**
```bash
# Create production environment flags
./scripts/setup-environment-flags.sh production
```

#### **Step 1.3: Test Environment Detection**

**On Both Servers:**
```bash
# Test environment detection
./scripts/detect-environment.sh

# Test auto performance mode
./scripts/auto-performance-mode.sh
```

### **Phase 2: Update Deployment Scripts**

#### **Step 2.1: Backup Current Deployment Script**

**On Both Servers:**
```bash
# Backup the current deployment script
sudo cp ./deploy_lara_rentals ./deploy_lara_rentals.backup.$(date +%Y%m%d)
```

#### **Step 2.2: Update Deployment Script**

**Create the new environment-aware deployment script on both servers:**

```bash
# Edit the deployment script
sudo nano ./deploy_lara_rentals
```

**Replace or add this content:**

```bash
#!/bin/bash

# Environment-Aware Laravel Deployment Script
# Auto-detects environment and applies appropriate performance settings

set -e

echo "🚀 Starting Laravel Deployment..."
echo "================================="

# Get current timestamp
TIMESTAMP=$(date '+%Y-%m-%d %H:%M:%S')
echo "📅 Deployment started: $TIMESTAMP"

# Detect environment
if [ -f "/path/to/your/laravel/project/scripts/detect-environment.sh" ]; then
    ENVIRONMENT=$(cd /path/to/your/laravel/project && ./scripts/detect-environment.sh)
    echo "🌍 Environment detected: $ENVIRONMENT"
else
    echo "⚠️  Environment detection script not found, assuming production"
    ENVIRONMENT="production"
fi

echo ""
echo "📦 Deployment Steps:"
echo "==================="

# Step 1: Navigate to project directory
echo "1️⃣  Navigating to project directory..."
cd /path/to/your/laravel/project

# Step 2: Pull latest code (your existing deployment steps)
echo "2️⃣  Pulling latest code..."
# Add your existing git pull commands here
# git pull origin develop  # for staging
# git pull origin master   # for production

# Step 3: Install/update dependencies
echo "3️⃣  Installing dependencies..."
# Add your existing composer install commands here
# composer install --no-dev --optimize-autoloader

# Step 4: Run migrations (if needed)
echo "4️⃣  Running database migrations..."
# Add your existing migration commands here
# php artisan migrate --force

# Step 5: Clear caches
echo "5️⃣  Clearing application caches..."
# Add your existing cache clearing commands here
# php artisan config:clear
# php artisan cache:clear
# php artisan view:clear

# Step 6: CRITICAL - Apply environment-aware performance settings
echo "6️⃣  Applying environment-aware performance settings..."
echo "🎯 This step prevents debugging extensions from slowing down the server"

if [ -f "./scripts/auto-performance-mode.sh" ]; then
    ./scripts/auto-performance-mode.sh
    echo "✅ Performance settings applied successfully"
else
    echo "⚠️  Auto performance script not found, applying manual fix..."
    
    # Fallback manual performance mode
    case "$ENVIRONMENT" in
        "production"|"staging")
            if [ -f "./scripts/server-performance-mode.sh" ]; then
                ./scripts/server-performance-mode.sh
            else
                echo "❌ No performance scripts found - MANUAL INTERVENTION REQUIRED"
                echo "🚨 Server may have performance issues!"
            fi
            ;;
    esac
fi

# Step 7: Final verification
echo "7️⃣  Final verification..."
if [ -f "./scripts/server-check-status.sh" ]; then
    ./scripts/server-check-status.sh
fi

# Step 8: Restart web server (if needed)
echo "8️⃣  Restarting web server..."
# Add your web server restart commands here if needed
# sudo systemctl reload apache2
# sudo systemctl reload nginx

echo ""
echo "🎉 Deployment Complete!"
echo "======================"
echo "📅 Completed: $(date '+%Y-%m-%d %H:%M:%S')"
echo "🌍 Environment: $ENVIRONMENT"
echo "⚡ Performance: Optimized"
echo "🔒 Security: $([ "$ENVIRONMENT" = "production" ] && echo "Maximum" || echo "Standard")"
echo ""
echo "✅ Server is ready for $([ "$ENVIRONMENT" = "production" ] && echo "live traffic" || echo "testing")!"
```

#### **Step 2.3: Make Deployment Script Executable**

**On Both Servers:**
```bash
sudo chmod +x ./deploy_lara_rentals
```

### **Phase 3: Test the New Deployment Process**

#### **Step 3.1: Test on Staging First**

```bash
# On staging server, test the new deployment script
sudo ./deploy_lara_rentals
```

**Expected Output:**
```
🚀 Starting Laravel Deployment...
🌍 Environment detected: staging
📦 Deployment Steps:
...
🧪 STAGING ENVIRONMENT DETECTED
✅ Staging performance mode enabled!
🎉 Deployment Complete!
```

#### **Step 3.2: Verify Performance Settings**

```bash
# Check that performance mode is active
./scripts/server-check-status.sh
```

**Expected Output:**
```
✅ Current Mode: PERFORMANCE MODE (optimal for production)
🟢 Xdebug: DISABLED (performance mode)
🟢 PCOV: DISABLED (performance mode)
🟢 Laravel Debugbar: DISABLED (performance mode)
```

---

## 🔄 Deployment Workflow

### **Normal Development Workflow**

```mermaid
graph TD
    A[Developer pushes to develop] --> B[Bitbucket Pipeline triggers]
    B --> C[SSH to staging server *************]
    C --> D[Run ./deploy_lara_rentals]
    D --> E[Auto-detect environment: staging]
    E --> F[Apply staging performance settings]
    F --> G[Staging ready for testing]
```

### **Production Release Workflow**

```mermaid
graph TD
    A[Merge develop to master] --> B[Bitbucket Pipeline triggers]
    B --> C[SSH to production server ***************]
    C --> D[Run ./deploy_lara_rentals]
    D --> E[Auto-detect environment: production]
    E --> F[Apply production performance settings]
    F --> G[Production ready for live traffic]
```

---

## 🛡️ Safety Features

### **Environment Detection Methods**

1. **Primary**: Server IP address detection
   - ************* → staging
   - *************** → production

2. **Secondary**: Environment flag files
   - `/etc/environment-staging`
   - `/etc/environment-production`

3. **Tertiary**: Hostname patterns
4. **Fallback**: Laravel .env APP_ENV setting

### **Performance Safeguards**

| Environment | Xdebug | PCOV | APP_DEBUG | Logging |
|-------------|--------|------|-----------|---------|
| **Production** | ❌ OFF | ❌ OFF | ❌ false | Error only |
| **Staging** | ❌ OFF | ❌ OFF | ❌ false | Warning+ |
| **Development** | 🟡 Available | 🟡 Available | 🟡 Configurable | Debug |

### **Automatic Recovery**

- **Failed detection**: Defaults to production (safest)
- **Missing scripts**: Falls back to manual performance mode
- **Configuration errors**: Applies safe defaults

---

## 🚨 Troubleshooting

### **Common Issues**

#### **Issue**: Environment not detected correctly
**Solution**:
```bash
# Check detection manually
./scripts/detect-environment.sh

# Check environment flags
ls -la /etc/environment-*
ls -la /var/www/STAGING /var/www/PRODUCTION
```

#### **Issue**: Performance mode not applied
**Solution**:
```bash
# Apply manually
./scripts/auto-performance-mode.sh

# Check status
./scripts/server-check-status.sh
```

#### **Issue**: Deployment script fails
**Solution**:
```bash
# Check script permissions
ls -la ./deploy_lara_rentals

# Check script syntax
bash -n ./deploy_lara_rentals

# Run with debug output
bash -x ./deploy_lara_rentals
```

### **Emergency Procedures**

#### **If Production Performance Issues Occur**

```bash
# SSH to production server immediately
ssh foufos@***************

# Apply emergency performance fix
cd /path/to/your/laravel/project
./scripts/server-performance-mode.sh

# Verify fix
./scripts/server-check-status.sh
```

#### **Rollback Deployment Script**

```bash
# Restore backup if needed
sudo cp ./deploy_lara_rentals.backup.YYYYMMDD ./deploy_lara_rentals
sudo chmod +x ./deploy_lara_rentals
```

---

## 📊 Monitoring & Verification

### **Post-Deployment Checks**

```bash
# 1. Environment verification
./scripts/detect-environment.sh

# 2. Performance status
./scripts/server-check-status.sh

# 3. Application health
curl -I https://your-domain.com

# 4. Response time test
curl -w "Time: %{time_total}s\n" -o /dev/null -s https://your-domain.com
```

### **Performance Benchmarks**

| Metric | Before Fix | After Fix | Target |
|--------|------------|-----------|--------|
| **Page Load Time** | 10+ seconds | 2-4 seconds | < 3 seconds |
| **Server Response** | Slow | Fast | < 500ms |
| **CPU Usage** | High | Normal | < 70% |
| **Memory Usage** | High | Normal | < 80% |

---

## 🎯 Success Criteria

### **Deployment Success Indicators**

- ✅ Environment correctly detected
- ✅ Performance mode automatically applied
- ✅ No debugging extensions enabled in production/staging
- ✅ Fast page load times (< 3 seconds)
- ✅ Normal server resource usage

### **Long-term Benefits**

1. **Automated Performance**: No manual intervention needed
2. **Environment Safety**: Production always optimized
3. **Developer Friendly**: Debugging available when needed
4. **Consistent Deployments**: Same process for all environments
5. **Easy Troubleshooting**: Clear status and detection tools

---

## 📝 Maintenance

### **Regular Tasks**

- **Monthly**: Verify environment flags are in place
- **Before major releases**: Test deployment process on staging
- **After server changes**: Re-run environment setup

### **Updates**

When updating the deployment process:
1. Test changes on staging first
2. Backup current deployment script
3. Apply changes to staging
4. Verify functionality
5. Apply to production

---

**The deployment process is now fully automated and environment-aware!** 🚀
