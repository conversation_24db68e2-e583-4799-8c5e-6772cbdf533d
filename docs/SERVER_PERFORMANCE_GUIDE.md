# Server Performance Management Guide

## Overview

This guide provides server-compatible performance management tools for your staging/production environment that doesn't use Docker. These scripts work directly with PHP configuration files and system services.

## 🚨 Problem Solved

**Issue**: Performance slowdowns on staging server (Digital Ocean) due to debugging extensions being enabled.

**Root Cause**: PCOV and/or XDEBUG extensions enabled in production environment.

**Solution**: Server-compatible performance management scripts that work without Dock<PERSON>.

---

## 🛠️ Server Scripts

### Core Management Scripts

```bash
./scripts/server-performance-mode.sh      # Default state (fast) - RECOMMENDED FOR STAGING/PRODUCTION
./scripts/server-development-mode.sh      # Full debugging (only for troubleshooting)
./scripts/server-check-status.sh          # Current configuration status
```

### Individual Tool Control

```bash
./scripts/server-toggle-debug.sh [on|off]     # Xdebug only
./scripts/server-toggle-coverage.sh [on|off]  # PCOV only
```

---

## 🚀 Quick Fix for Staging Performance

**For immediate performance improvement on your staging server:**

```bash
# 1. Upload the server scripts to your staging server
# 2. Run performance mode to disable all debugging
./scripts/server-performance-mode.sh

# 3. Check the results
./scripts/server-check-status.sh
```

**Expected result**: Significant performance improvement by disabling PCOV and XDEBUG.

---

## 📋 Detailed Usage

### 1. **Performance Mode (Recommended for Staging/Production)**

```bash
./scripts/server-performance-mode.sh
```

**What it does:**
- Disables Xdebug debug mode (`xdebug.mode=off`)
- Disables PCOV coverage (`pcov.enabled=0`)
- Sets `APP_DEBUG=false` (disables Laravel Debugbar)
- Reloads web server (Apache/Nginx + PHP-FPM)

**When to use:**
- ✅ **Always on staging/production servers**
- ✅ Normal development work
- ✅ When you need maximum performance

### 2. **Development Mode (Only for Debugging)**

```bash
./scripts/server-development-mode.sh
```

**What it does:**
- Enables Xdebug debug mode (`xdebug.mode=debug`)
- Sets `APP_DEBUG=true` (enables Laravel Debugbar)
- Keeps PCOV disabled
- Reloads web server

**When to use:**
- 🔧 Only when actively debugging issues on staging
- 🔧 Temporary troubleshooting sessions
- ⚠️ **Always return to performance mode when done**

### 3. **Check Current Status**

```bash
./scripts/server-check-status.sh
```

**What it shows:**
- Current Xdebug status
- Current PCOV status
- Current Laravel Debugbar status
- Performance test results
- PHP configuration info
- Recommended actions

---

## 🔧 How It Works

### Configuration Management

The scripts create override configuration files in your PHP configuration directory:

```bash
# Typical locations:
/etc/php/8.1/apache2/conf.d/99-xdebug-performance.ini
/etc/php/8.1/apache2/conf.d/99-pcov-performance.ini

# Or for PHP-FPM:
/etc/php/8.1/fpm/conf.d/99-xdebug-performance.ini
/etc/php/8.1/fpm/conf.d/99-pcov-performance.ini
```

### Web Server Restart

Scripts automatically detect and restart the appropriate service:
- **Apache**: `sudo systemctl reload apache2`
- **Nginx + PHP-FPM**: `sudo systemctl reload nginx && sudo systemctl reload php*-fpm`
- **Fallback**: Uses `service` command for older systems

---

## 🚨 Staging Server Deployment

### Step 1: Upload Scripts

```bash
# On your staging server, navigate to your project directory
cd /path/to/your/laravel/project

# Make sure the scripts directory exists and upload the server-* scripts
# The scripts should be in: scripts/server-*.sh
```

### Step 2: Set Permissions

```bash
# Make scripts executable
chmod +x scripts/server-*.sh
```

### Step 3: Enable Performance Mode

```bash
# This should be your default state for staging/production
./scripts/server-performance-mode.sh
```

### Step 4: Verify Results

```bash
# Check that everything is properly configured
./scripts/server-check-status.sh

# Test page load performance
curl -w "Time: %{time_total}s\n" -o /dev/null -s https://your-staging-domain.com
```

---

## 🔍 Troubleshooting

### Issue: Scripts fail with permission errors

**Solution:**
```bash
# Ensure you have sudo access
sudo -v

# Or run scripts with sudo if needed
sudo ./scripts/server-performance-mode.sh
```

### Issue: PHP configuration directory not found

**Solution:**
```bash
# Check your PHP configuration
php --ini

# Manually set the directory in the script if needed
# Edit the PHP_INI_DIR variable in the scripts
```

### Issue: Web server doesn't restart automatically

**Solution:**
```bash
# Manually restart your web server after running scripts
sudo systemctl reload apache2
# OR
sudo systemctl reload nginx
sudo systemctl reload php8.1-fpm  # adjust version as needed
```

### Issue: Still slow after disabling debugging

**Check other factors:**
```bash
# 1. Verify extensions are actually disabled
./scripts/server-check-status.sh

# 2. Check database performance
php artisan migrate:status

# 3. Check disk space and memory
df -h
free -h

# 4. Check for other performance issues
tail -f /var/log/apache2/error.log  # or nginx error log
```

---

## 📊 Performance Expectations

| Configuration | Expected Performance | Use Case |
|---------------|---------------------|----------|
| **Performance Mode** | Fast page loads | ✅ Staging/Production |
| **Development Mode** | Slower (debugging overhead) | 🔧 Troubleshooting only |
| **Coverage Mode** | Slowest (coverage overhead) | 🧪 Testing only |

---

## 🔒 Security Notes

- Scripts require `sudo` access to modify PHP configuration
- Configuration files are created with appropriate permissions
- Web server reloads are done safely without stopping services
- No sensitive information is logged or exposed

---

## 📁 Files Created

### Server Scripts
- `scripts/server-performance-mode.sh` - Main performance script
- `scripts/server-development-mode.sh` - Debugging mode
- `scripts/server-toggle-debug.sh` - Xdebug control
- `scripts/server-toggle-coverage.sh` - PCOV control
- `scripts/server-check-status.sh` - Status monitoring

### Configuration Files (Created automatically)
- `99-xdebug-performance.ini` - Xdebug configuration override
- `99-pcov-performance.ini` - PCOV configuration override

---

## 🎯 Best Practices

### For Staging/Production Servers

1. **Always use performance mode by default**
   ```bash
   ./scripts/server-performance-mode.sh
   ```

2. **Only enable debugging when actively troubleshooting**
   ```bash
   ./scripts/server-development-mode.sh
   # ... debug your issue ...
   ./scripts/server-performance-mode.sh  # Return to performance mode
   ```

3. **Regular status checks**
   ```bash
   ./scripts/server-check-status.sh
   ```

4. **Monitor performance**
   ```bash
   # Add to your monitoring/deployment scripts
   curl -w "Time: %{time_total}s\n" -o /dev/null -s https://your-domain.com
   ```

### For Development Servers

- Use the Docker-based scripts (`scripts/performance-mode.sh`, etc.)
- Server scripts are specifically for non-Docker environments

---

## 🚀 Quick Reference

```bash
# STAGING/PRODUCTION (Server Scripts)
./scripts/server-performance-mode.sh    # ← Use this by default
./scripts/server-check-status.sh        # ← Check current state
./scripts/server-development-mode.sh    # ← Only for debugging

# LOCAL DEVELOPMENT (Docker Scripts)  
./scripts/performance-mode.sh           # ← Use this by default
./scripts/check-status.sh               # ← Check current state
./scripts/development-mode.sh           # ← For debugging
```

---

## ✅ Success Criteria

After implementing these scripts on your staging server, you should see:

- ✅ **Faster page load times** (debugging overhead removed)
- ✅ **Reduced server resource usage** (CPU/memory)
- ✅ **Easy debugging when needed** (temporary development mode)
- ✅ **Clear status monitoring** (know exactly what's enabled)
- ✅ **Consistent performance** (reliable staging environment)

The main performance bottleneck should be resolved!
