<ul class="nav nav-tabs mb-3" id="custom-content-below-tab" role="tablist">
    @foreach($languages as $locale => $language)
        <li class="nav-item">
            <a class="nav-link @if($locale === array_key_first($languages)) active @endif" id="tab-{!! $locale !!}-tab" data-toggle="pill" href="#tab-{!! $locale !!}" role="tab" aria-controls="tab-{!! $locale !!}" aria-selected="true">{!! $language !!}</a>
        </li>
    @endforeach
</ul>
<div class="tab-content">
    @foreach($languages as $locale => $language)
        <div class="tab-pane @if($locale === array_key_first($languages)) active show @endif fade" id="tab-{!! $locale !!}" role="tabpanel" aria-labelledby="tab-{!! $locale !!}-tab">
            <div class="row mt-4 mb-4">
                <div class="col-sm-12">
                    <h3 class="card-title"><strong>Slug:</strong> {{ $post->translate($locale)->slug ?? '' }}</h3>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12">
                    <div class="form-group">
                        {!! Form::label('title', 'Title*') !!}
                        {!! Form::text($locale . '[title]', $post->translate($locale)->title ?? '',['id' => 'title-' . $locale . '-' . $post->id, 'class' => 'form-control' . ($errors->has('title' . $locale) ? ' is-invalid' : null)]) !!}
                        @include('hodor::common._translationSuggestCta', ['model' => $post, 'field' => 'title', 'locale' => $locale, 'wysiwyg' => 'no'])
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12">
                    <div class="form-group">
                        <label for="meta_title">
                            Meta Title*
                            <code>50-60 chars</code>
                        </label>
                        {!! Form::text($locale . '[meta_title]', $post->translate($locale)->meta_title ?? '', ['id' => 'meta_title-' . $locale . '-' . $post->id, 'class' => 'form-control' . ($errors->has($locale . '.meta_title') ? ' is-invalid' : null)]) !!}
                        <p><span id="seo_title_count">{{ strlen($post->meta_title) }}</span> characters</p>
                        @include('hodor::common._translationSuggestCta', ['model' => $post, 'field' => 'meta_title', 'locale' => $locale, 'wysiwyg' => 'no'])
                    </div>
                </div>
            </div>
            <div class="col-sm-12">
                <div class="form-group">
                    <label for="meta_description">
                        Meta Description*
                        <code>120-158 chars</code>
                    </label>
                    {!! Form::textarea($locale . '[meta_description]', $post->translate($locale)->meta_description ?? '', ['id' => 'meta_description-' . $locale . '-' . $post->id, 'class' => 'form-control' . ($errors->has($locale . '.meta_description') ? ' is-invalid' : null), 'rows' => '3',]) !!}
                    <p><span id="seo_description_count">{{ strlen($post->meta_description) }}</span> characters</p>
                    @include('hodor::common._translationSuggestCta', ['model' => $post, 'field' => 'meta_description', 'locale' => $locale, 'wysiwyg' => 'no'])
                </div>
            </div>
            <div class="col-sm-12">
                <div class="form-group">
                    <label for="content">
                        Content*
                        <code>Inline photos pattern: [Photo id=666]</code>
                    </label>
                    {!! Form::textarea($locale . '[content]', $post->translate($locale)->content ?? '', ['id' => 'content-' . $locale . '-' . $post->id, 'class' => 'summernote form-control' . ($errors->has($locale . '.content') ? ' is-invalid' : null), 'rows' => '15']) !!}
                    @include('hodor::common._translationSuggestCta', ['model' => $post, 'field' => 'content', 'locale' => $locale, 'wysiwyg' => 'yes'])
                </div>
            </div>
        </div>
    @endforeach
</div>
<div class="row">
    <div class="col-lg-6">
        <div class="form-group">
            {!! Form::label('motifs', 'Motifs*') !!}
            {!! Form::select('motifs[]', $motifs, null, ['multiple' => true, 'class' => 'form-control custom-select select2']) !!}
        </div>
    </div>
    <div class="col-lg-6">
        <div class="form-group">
            {!! Form::label('tags', 'Tags') !!}
            {!! Form::select('tags[]', $tags, null, ['multiple' => true, 'class' => 'form-control custom-select select2']) !!}
        </div>
    </div>
</div>
<div class="row">
    <div class="col-lg-6">
        <div class="form-group">
            {!! Form::label('localised_motifs', 'Localised Motifs*') !!}
            {!! Form::select('localised_motifs[]', $localised_motifs, null, ['multiple' => true, 'class' => 'form-control custom-select select2']) !!}
        </div>
    </div>
</div>
<div class="row">
    <div class="col-lg-6">
        <div class="form-group">
            {!! Form::label('featured', 'Featured*') !!}
            {!! Form::select('featured', array('0' => 'No', '1' => 'Yes'), null, ['class' => 'form-control custom-select']) !!}
        </div>
    </div>
</div>
<div class="row">
    <div class="col-lg-6">
        <div class="form-group">
            {!! Form::label('published', 'Published*') !!}
            {!! Form::select('published', array('0' => 'No', '1' => 'Yes'), null, ['class' => 'form-control custom-select']) !!}
        </div>
    </div>
</div>

@section('css')
    <link rel="stylesheet" href="/vendor/summernote/summernote-bs4.min.css">
    @parent
@stop

@section('js')
    <script src="/vendor/summernote/summernote-bs4.min.js"></script>
    <script type="text/javascript">
        $('#seo_description').keyup(function() {
            var characterCountDescr = $(this).val().length,
                current_count_descr = $('#seo_description_count');
            current_count_descr.text(characterCountDescr);
        });

        $('#seo_title').keyup(function() {
            var characterCountTitle = $(this).val().length,
                current_count_title = $('#seo_title_count');
            current_count_title.text(characterCountTitle);
        });
    </script>
    @parent
@stop
