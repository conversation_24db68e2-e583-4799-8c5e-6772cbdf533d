/.phpunit.cache
/node_modules
# /public/build
/public/hot
/public/storage
/storage/*.key
/vendor
.env
.env.backup
.env.production
.phpunit.result.cache
Homestead.json
Homestead.yaml
auth.json
npm-debug.log
yarn-error.log
/.fleet
/.idea
/.vscode

/public/img/*
/public/media/*
.project
.DS_Store
/.settings
/.buildpath
/storage/translations_export/*
/storage/media-library/*
/public/sitemap.xml
/public/sitemap_index.xml
/public/sitemap_en.xml
/public/sitemap_el.xml
/public/sitemap_fr.xml
/public/sitemap_de.xml
/public/sitemap_it.xml
/public/sitemap_ru.xml
/storage/tmp

# SECURITY: Sensitive development files (should not be deployed to production)
# Note: These are needed for development but should be excluded from production
# /scripts/
# /docs/

# Test Coverage Reports
/coverage-html
/coverage-xml
/coverage-unit
/coverage-feature
/coverage-helpers
/coverage-combined
/coverage-database
/coverage-models
/coverage-controllers
/coverage*
coverage.xml
coverage.txt
.phpunit.cache
