#!/bin/bash

# Test Coverage Script for Laravel Rentals
# This script provides various coverage reporting options

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Docker is running
check_docker() {
    if ! docker compose ps | grep -q "rentals"; then
        print_error "Docker containers are not running. Please start them first:"
        echo "  docker compose up -d"
        exit 1
    fi
}

# Check if coverage driver is available
check_coverage_driver() {
    print_status "Checking coverage driver availability..."

    if docker compose exec rentals php -m | grep -q "pcov"; then
        print_success "PCOV is available and will be used for coverage"
        return 0
    elif docker compose exec rentals php -m | grep -q "xdebug"; then
        print_warning "PCOV not found, falling back to Xdebug"
        return 0
    else
        print_error "No coverage driver found. Please install PCOV or Xdebug"
        exit 1
    fi
}

# Function to run tests with coverage
run_coverage() {
    local format=$1
    local output_file=$2

    print_status "Enabling coverage extensions temporarily..."
    # Enable PCOV for coverage
    docker compose exec rentals bash -c "sed -i 's/pcov.enabled=.*/pcov.enabled=1/' /usr/local/etc/php/conf.d/docker-php-ext-pcov.ini"

    print_status "Running tests with $format coverage..."

    case $format in
        "html")
            print_status "Generating HTML coverage report (this may take a while)..."
            docker compose exec -e XDEBUG_MODE=coverage rentals php -d memory_limit=2G -d max_execution_time=600 artisan test --coverage-html coverage-html
            print_success "HTML coverage report generated in coverage-html/"
            ;;
        "text")
            if [ -n "$output_file" ]; then
                docker compose exec -e XDEBUG_MODE=coverage rentals php -d memory_limit=2G -d max_execution_time=600 artisan test --coverage-text > "$output_file"
                print_success "Text coverage report saved to $output_file"
            else
                docker compose exec -e XDEBUG_MODE=coverage rentals php -d memory_limit=2G -d max_execution_time=600 artisan test --coverage-text
            fi
            ;;
        "clover")
            print_status "Generating Clover XML coverage report (this may take a while)..."
            docker compose exec -e XDEBUG_MODE=coverage rentals php -d memory_limit=2G -d max_execution_time=600 artisan test --coverage-clover coverage.xml
            print_success "Clover XML coverage report generated: coverage.xml"
            ;;
        "summary")
            docker compose exec -e XDEBUG_MODE=coverage rentals php -d memory_limit=2G -d max_execution_time=600 artisan test --coverage
            ;;
        *)
            print_error "Unknown coverage format: $format"
            exit 1
            ;;
    esac

    # Disable PCOV after coverage to restore performance
    print_status "Disabling coverage extensions to restore performance..."
    docker compose exec rentals bash -c "sed -i 's/pcov.enabled=.*/pcov.enabled=0/' /usr/local/etc/php/conf.d/docker-php-ext-pcov.ini"
}

# Function to open HTML coverage report
open_coverage() {
    if [ -f "coverage-html/index.html" ]; then
        print_status "Opening coverage report..."
        if command -v open >/dev/null 2>&1; then
            open coverage-html/index.html
        elif command -v xdg-open >/dev/null 2>&1; then
            xdg-open coverage-html/index.html
        else
            print_warning "Cannot auto-open browser. Please open coverage-html/index.html manually"
        fi
    else
        print_error "HTML coverage report not found. Run with --html first"
        exit 1
    fi
}

# Function to clean coverage files
clean_coverage() {
    print_status "Cleaning coverage files..."
    rm -rf coverage-html coverage-xml coverage.xml coverage.txt .phpunit.cache
    print_success "Coverage files cleaned"
}

# Function to show coverage statistics
show_stats() {
    if [ -f "coverage.xml" ]; then
        print_status "Coverage Statistics:"
        # Extract coverage percentage from clover XML
        if command -v xmllint >/dev/null 2>&1; then
            local lines_covered=$(xmllint --xpath "//metrics/@coveredstatements" coverage.xml 2>/dev/null | sed 's/.*="\([^"]*\)".*/\1/')
            local lines_total=$(xmllint --xpath "//metrics/@statements" coverage.xml 2>/dev/null | sed 's/.*="\([^"]*\)".*/\1/')

            if [ -n "$lines_covered" ] && [ -n "$lines_total" ] && [ "$lines_total" -gt 0 ]; then
                local percentage=$(echo "scale=2; $lines_covered * 100 / $lines_total" | bc 2>/dev/null || echo "N/A")
                echo "  Lines Covered: $lines_covered / $lines_total ($percentage%)"
            fi
        fi
    else
        print_warning "No coverage.xml found. Run with --clover first"
    fi
}

# Main script logic
main() {
    case "${1:-summary}" in
        "--html"|"-h")
            check_docker
            check_coverage_driver
            run_coverage "html"
            ;;
        "--text"|"-t")
            check_docker
            check_coverage_driver
            run_coverage "text" "${2:-coverage.txt}"
            ;;
        "--clover"|"-c")
            check_docker
            check_coverage_driver
            run_coverage "clover"
            ;;
        "--summary"|"-s"|"")
            check_docker
            check_coverage_driver
            run_coverage "summary"
            ;;
        "--open"|"-o")
            open_coverage
            ;;
        "--clean")
            clean_coverage
            ;;
        "--stats")
            show_stats
            ;;
        "--all"|"-a")
            check_docker
            check_coverage_driver
            run_coverage "html"
            run_coverage "clover"
            run_coverage "text" "coverage.txt"
            show_stats
            ;;
        "--help")
            echo "Laravel Rentals Test Coverage Script"
            echo ""
            echo "Usage: $0 [option]"
            echo ""
            echo "Options:"
            echo "  --html, -h        Generate HTML coverage report"
            echo "  --text, -t [file] Generate text coverage report (optional output file)"
            echo "  --clover, -c      Generate Clover XML coverage report"
            echo "  --summary, -s     Show coverage summary (default)"
            echo "  --open, -o        Open HTML coverage report in browser"
            echo "  --clean           Clean all coverage files"
            echo "  --stats           Show coverage statistics"
            echo "  --all, -a         Generate all coverage formats"
            echo "  --help            Show this help message"
            echo ""
            echo "Examples:"
            echo "  $0                # Run with summary"
            echo "  $0 --html         # Generate HTML report"
            echo "  $0 --text         # Generate text report"
            echo "  $0 --all          # Generate all formats"
            echo "  $0 --open         # Open HTML report"
            ;;
        *)
            print_error "Unknown option: $1"
            echo "Use --help for usage information"
            exit 1
            ;;
    esac
}

# Run main function with all arguments
main "$@"
