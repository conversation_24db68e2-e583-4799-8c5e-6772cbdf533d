#!/bin/bash

# Check current performance and debugging status
# Usage: ./scripts/check-status.sh

set -e

echo "🔍 Checking current development environment status..."
echo ""

# Check if Docker is running
if ! docker compose ps | grep -q "rentals"; then
    echo "❌ Docker containers are not running"
    echo "   Run: docker compose up -d"
    exit 1
fi

echo "📊 Current Configuration Status:"
echo "================================"

# Check Xdebug status
XDEBUG_MODE=$(docker compose exec rentals php -r "echo ini_get('xdebug.mode');" 2>/dev/null | grep -v "Deprecated" | grep -v "Warning" | tr -d '\r\n' || echo "unknown")
if [ "$XDEBUG_MODE" = "" ] || [ "$XDEBUG_MODE" = "off" ]; then
    echo "🟢 Xdebug: DISABLED (performance mode)"
else
    echo "🟡 Xdebug: ENABLED ($XDEBUG_MODE mode)"
fi

# Check PCOV status
PCOV_ENABLED=$(docker compose exec rentals php -r "echo ini_get('pcov.enabled');" 2>/dev/null | grep -v "Deprecated" | grep -v "Warning" | tr -d '\r\n' || echo "unknown")
if [ "$PCOV_ENABLED" = "0" ] || [ "$PCOV_ENABLED" = "" ]; then
    echo "🟢 PCOV: DISABLED (performance mode)"
else
    echo "🟡 PCOV: ENABLED (coverage mode)"
fi

# Check APP_DEBUG status
APP_DEBUG=$(docker compose exec rentals grep "APP_DEBUG=" /var/www/html/.env 2>/dev/null | cut -d'=' -f2 | tr -d '\r\n' || echo "unknown")
if [ "$APP_DEBUG" = "false" ]; then
    echo "🟢 Laravel Debugbar: DISABLED (performance mode)"
else
    echo "🟡 Laravel Debugbar: ENABLED (debug mode)"
fi

echo ""

# Determine current mode
if [ "$XDEBUG_MODE" = "" ] || [ "$XDEBUG_MODE" = "off" ] && [ "$PCOV_ENABLED" = "0" ] && [ "$APP_DEBUG" = "false" ]; then
    echo "✅ Current Mode: PERFORMANCE MODE (optimal for development)"
    echo ""
    echo "💡 Available actions:"
    echo "   ./scripts/development-mode.sh    - Enable debugging tools"
    echo "   ./scripts/toggle-coverage.sh on  - Enable coverage for testing"
    echo "   composer test-coverage-html      - Run tests with coverage"
elif [ "$XDEBUG_MODE" != "" ] && [ "$XDEBUG_MODE" != "off" ] && [ "$APP_DEBUG" = "true" ]; then
    echo "🛠️  Current Mode: DEVELOPMENT MODE (debugging enabled)"
    echo ""
    echo "💡 Available actions:"
    echo "   ./scripts/performance-mode.sh     - Return to performance mode"
    echo "   ./scripts/toggle-coverage.sh on   - Enable coverage for testing"
    echo "   composer test-coverage-html       - Run tests with coverage"
elif [ "$PCOV_ENABLED" = "1" ]; then
    echo "🧪 Current Mode: COVERAGE MODE (testing with coverage)"
    echo ""
    echo "💡 Available actions:"
    echo "   ./scripts/toggle-coverage.sh off  - Disable coverage"
    echo "   ./scripts/performance-mode.sh     - Return to performance mode"
    echo "   composer test-coverage-html       - Run tests with coverage"
else
    echo "⚠️  Current Mode: MIXED/CUSTOM CONFIGURATION"
    echo ""
    echo "💡 Recommended actions:"
    echo "   ./scripts/performance-mode.sh     - Reset to performance mode"
    echo "   ./scripts/development-mode.sh     - Enable full debugging"
fi

echo ""

# Performance test
echo "⚡ Quick Performance Test:"
echo "========================="
echo "Testing PHP execution speed..."

PERF_TIME=$(docker compose exec rentals php -r "
\$start = microtime(true);
for(\$i = 0; \$i < 100000; \$i++) { \$x = \$i * 2; }
\$end = microtime(true);
echo number_format((\$end - \$start) * 1000, 2);
" 2>/dev/null | grep -v "Deprecated" | grep -v "Warning" | tr -d '\r\n')

echo "PHP execution time: ${PERF_TIME}ms"

if (( $(echo "$PERF_TIME < 10" | bc -l) )); then
    echo "🟢 PHP Performance: EXCELLENT"
elif (( $(echo "$PERF_TIME < 50" | bc -l) )); then
    echo "🟡 PHP Performance: GOOD"
else
    echo "🔴 PHP Performance: SLOW (check debugging extensions)"
fi

echo ""
echo "📋 Quick Reference:"
echo "=================="
echo "Performance Mode:   ./scripts/performance-mode.sh"
echo "Development Mode:   ./scripts/development-mode.sh"
echo "Toggle Xdebug:      ./scripts/toggle-debug.sh [on|off]"
echo "Toggle Coverage:    ./scripts/toggle-coverage.sh [on|off]"
echo "Check Status:       ./scripts/check-status.sh"
echo "Test Coverage:      composer test-coverage-html"
