#!/bin/bash

# Automatic Performance Mode Script with Environment Detection
# Usage: ./scripts/auto-performance-mode.sh
# Automatically applies the correct performance settings based on detected environment

set -e

# Get the directory where this script is located
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Source the environment detection script
if [ -f "$SCRIPT_DIR/detect-environment.sh" ]; then
    source "$SCRIPT_DIR/detect-environment.sh"
else
    echo "❌ Error: detect-environment.sh not found in $SCRIPT_DIR"
    exit 1
fi

# Detect current environment
ENVIRONMENT=$(detect_environment)

echo "🔍 Environment Detection Results:"
echo "================================="
echo "🌍 Detected Environment: $ENVIRONMENT"
echo "🖥️  Server IP: $(hostname -I | awk '{print $1}' 2>/dev/null || echo 'unknown')"
echo "🏷️  Hostname: $(hostname 2>/dev/null || echo 'unknown')"
echo ""

# Apply environment-specific configuration
case "$ENVIRONMENT" in
    "production")
        echo "🚀 PRODUCTION ENVIRONMENT DETECTED"
        echo "   Applying maximum performance optimizations..."
        echo "   - Disabling ALL debugging extensions"
        echo "   - Setting APP_DEBUG=false"
        echo "   - Optimizing for live traffic"
        echo ""
        
        if [ -f "$SCRIPT_DIR/server-performance-mode.sh" ]; then
            "$SCRIPT_DIR/server-performance-mode.sh"
        else
            echo "❌ Error: server-performance-mode.sh not found"
            exit 1
        fi
        
        # Additional production-specific optimizations
        echo "🔧 Applying additional production optimizations..."
        
        # Ensure APP_ENV is set to production
        if [ -f ".env" ]; then
            sed -i.bak 's/APP_ENV=.*/APP_ENV=production/' .env
            sed -i.bak 's/APP_DEBUG=.*/APP_DEBUG=false/' .env
        fi
        
        echo "✅ Production performance mode enabled!"
        ;;
        
    "staging")
        echo "🧪 STAGING ENVIRONMENT DETECTED"
        echo "   Applying performance optimizations with minimal debugging..."
        echo "   - Disabling performance-heavy extensions"
        echo "   - Setting APP_DEBUG=false"
        echo "   - Optimizing for testing workflows"
        echo ""
        
        if [ -f "$SCRIPT_DIR/server-performance-mode.sh" ]; then
            "$SCRIPT_DIR/server-performance-mode.sh"
        else
            echo "❌ Error: server-performance-mode.sh not found"
            exit 1
        fi
        
        # Staging-specific configuration
        if [ -f ".env" ]; then
            sed -i.bak 's/APP_ENV=.*/APP_ENV=staging/' .env
            sed -i.bak 's/APP_DEBUG=.*/APP_DEBUG=false/' .env
        fi
        
        echo "✅ Staging performance mode enabled!"
        ;;
        
    "development"|"local")
        echo "🛠️  DEVELOPMENT ENVIRONMENT DETECTED"
        echo "   Applying development-friendly configuration..."
        echo "   - Performance mode enabled by default"
        echo "   - Debugging tools available on-demand"
        echo "   - Use toggle scripts when debugging needed"
        echo ""
        
        # For development, we still default to performance mode
        # but make debugging easily available
        if [ -f "$SCRIPT_DIR/performance-mode.sh" ]; then
            # Use Docker version for local development
            "$SCRIPT_DIR/performance-mode.sh"
        elif [ -f "$SCRIPT_DIR/server-performance-mode.sh" ]; then
            # Fallback to server version
            "$SCRIPT_DIR/server-performance-mode.sh"
        else
            echo "❌ Error: No performance mode script found"
            exit 1
        fi
        
        echo "✅ Development performance mode enabled!"
        echo ""
        echo "💡 Development Tips:"
        echo "   - Use ./scripts/development-mode.sh when debugging"
        echo "   - Use ./scripts/toggle-debug.sh on/off for quick Xdebug toggle"
        echo "   - Use ./scripts/toggle-coverage.sh on/off for testing"
        ;;
        
    *)
        echo "⚠️  UNKNOWN ENVIRONMENT: $ENVIRONMENT"
        echo "   Applying safe default configuration..."
        echo ""
        
        # Default to performance mode for safety
        if [ -f "$SCRIPT_DIR/server-performance-mode.sh" ]; then
            "$SCRIPT_DIR/server-performance-mode.sh"
        else
            echo "❌ Error: server-performance-mode.sh not found"
            exit 1
        fi
        
        echo "✅ Default performance mode enabled!"
        ;;
esac

echo ""
echo "📊 Final Status Check:"
echo "====================="

# Run status check if available
if [ -f "$SCRIPT_DIR/server-check-status.sh" ]; then
    "$SCRIPT_DIR/server-check-status.sh"
elif [ -f "$SCRIPT_DIR/check-status.sh" ]; then
    "$SCRIPT_DIR/check-status.sh"
else
    echo "ℹ️  Status check script not available"
fi

echo ""
echo "🎉 Environment-aware performance configuration complete!"
echo "   Environment: $ENVIRONMENT"
echo "   Performance: Optimized"
echo "   Ready for: $([ "$ENVIRONMENT" = "production" ] && echo "Live Traffic" || echo "Testing & Development")"
