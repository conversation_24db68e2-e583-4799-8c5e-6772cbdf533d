#!/bin/bash

# Enable performance mode by disabling all debugging/coverage extensions
# Usage: ./scripts/performance-mode.sh

set -e

echo "🚀 Enabling performance mode (default state)..."
echo "   - Disabling Xdebug debug mode"
echo "   - Disabling PCOV coverage"
echo "   - Disabling <PERSON><PERSON> Debugbar"

# Disable Xdebug debug mode
docker compose exec rentals bash -c "echo 'xdebug.mode=off' > /usr/local/etc/php/conf.d/99-xdebug-mode.ini"

# Disable PCOV coverage
docker compose exec rentals bash -c "echo 'pcov.enabled=0' > /usr/local/etc/php/conf.d/99-pcov-enabled.ini"

# Disable Laravel Debugbar by temporarily setting APP_DEBUG=false
docker compose exec rentals bash -c "sed -i 's/APP_DEBUG=true/APP_DEBUG=false/' /var/www/html/.env"

echo "🔄 Restarting container to apply changes..."
docker compose restart rentals

echo "✅ Performance mode enabled (default state restored)!"
echo ""
echo "📊 Current status:"
echo "   Xdebug mode: off"
echo "   PCOV coverage: disabled"
echo "   Laravel Debugbar: disabled"
echo ""
echo "💡 For development with debugging: ./scripts/development-mode.sh"
echo "💡 To enable only Xdebug: ./scripts/toggle-debug.sh on"
echo "💡 To enable coverage for testing: ./scripts/toggle-coverage.sh on"
echo "💡 To run tests with coverage: composer test-coverage-html"
