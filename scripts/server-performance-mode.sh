#!/bin/bash

# Enable performance mode by disabling all debugging/coverage extensions (SERVER VERSION)
# Usage: ./scripts/server-performance-mode.sh

set -e

echo "🚀 Enabling performance mode (server version)..."
echo "   - Disabling Xdebug debug mode"
echo "   - Disabling PCOV coverage"
echo "   - Disabling Laravel Debugbar"

# Find PHP configuration directory
PHP_INI_DIR=$(php --ini | grep "Scan for additional .ini files" | cut -d: -f2 | xargs)
if [ -z "$PHP_INI_DIR" ] || [ "$PHP_INI_DIR" = "(none)" ]; then
    PHP_INI_DIR="/etc/php/$(php -r 'echo PHP_MAJOR_VERSION.".".PHP_MINOR_VERSION;')/apache2/conf.d"
    if [ ! -d "$PHP_INI_DIR" ]; then
        PHP_INI_DIR="/etc/php/$(php -r 'echo PHP_MAJOR_VERSION.".".PHP_MINOR_VERSION;')/fpm/conf.d"
    fi
    if [ ! -d "$PHP_INI_DIR" ]; then
        PHP_INI_DIR="/usr/local/etc/php/conf.d"
    fi
fi

echo "📁 Using PHP config directory: $PHP_INI_DIR"

# Create override configuration files
echo "🔧 Creating performance configuration files..."

# Disable Xdebug debug mode
sudo tee "$PHP_INI_DIR/99-xdebug-performance.ini" > /dev/null << 'EOF'
; Performance mode - Xdebug disabled
xdebug.mode=off
EOF

# Disable PCOV coverage
sudo tee "$PHP_INI_DIR/99-pcov-performance.ini" > /dev/null << 'EOF'
; Performance mode - PCOV disabled
pcov.enabled=0
EOF

# Disable Laravel Debugbar by setting APP_DEBUG=false
if [ -f ".env" ]; then
    echo "🔧 Disabling Laravel Debugbar..."
    sed -i.bak 's/APP_DEBUG=true/APP_DEBUG=false/' .env
fi

# Restart web server
echo "🔄 Restarting web server to apply changes..."
if systemctl is-active --quiet apache2; then
    sudo systemctl reload apache2
elif systemctl is-active --quiet nginx; then
    sudo systemctl reload nginx
    if systemctl is-active --quiet php*-fpm; then
        sudo systemctl reload php*-fpm
    fi
elif command -v service >/dev/null 2>&1; then
    # Fallback for older systems
    sudo service apache2 reload 2>/dev/null || sudo service nginx reload 2>/dev/null || true
    sudo service php*-fpm reload 2>/dev/null || true
else
    echo "⚠️  Could not automatically restart web server. Please restart manually."
fi

echo "✅ Performance mode enabled (server version)!"
echo ""
echo "📊 Current status:"
echo "   Xdebug mode: off"
echo "   PCOV coverage: disabled"
echo "   Laravel Debugbar: disabled"
echo ""
echo "💡 For development with debugging: ./scripts/server-development-mode.sh"
echo "💡 To enable only Xdebug: ./scripts/server-toggle-debug.sh on"
echo "💡 To enable coverage for testing: ./scripts/server-toggle-coverage.sh on"
echo "💡 To check status: ./scripts/server-check-status.sh"
