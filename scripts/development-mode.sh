#!/bin/bash

# Enable development mode with debugging tools
# Usage: ./scripts/development-mode.sh

set -e

echo "🛠️  Enabling development mode..."
echo "   - Enabling Xdebug debug mode"
echo "   - Enabling <PERSON><PERSON> Debugbar"
echo "   - PCOV remains disabled (use coverage scripts for testing)"

# Enable Xdebug debug mode by modifying the original config file
docker compose exec rentals bash -c "sed -i 's/xdebug.mode=.*/xdebug.mode=debug/' /usr/local/etc/php/conf.d/docker-php-ext-xdebug.ini"

# Enable Laravel Debugbar by setting APP_DEBUG=true
docker compose exec rentals bash -c "sed -i 's/APP_DEBUG=false/APP_DEBUG=true/' /var/www/html/.env"

echo "🔄 Restarting container to apply changes..."
docker compose restart rentals

echo "✅ Development mode enabled!"
echo ""
echo "📊 Current status:"
echo "   Xdebug mode: debug"
echo "   PCOV coverage: disabled (use coverage scripts when needed)"
echo "   Laravel Debugbar: enabled"
echo ""
echo "💡 To return to performance mode: ./scripts/performance-mode.sh"
echo "💡 To enable coverage for testing: ./scripts/toggle-coverage.sh on"
echo "💡 To run tests with coverage: composer test-coverage-html"
echo ""
echo "🚨 Note: Development mode will slow down the application"
echo "   Use performance mode for normal development work"
