#!/bin/bash

# Environment-Aware Container Preparation Script
# This replaces the problematic docker/rentals/prepare-container.sh
# Usage: ./scripts/prepare-container-environment-aware.sh

set -e

# Get the directory where this script is located
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Source the environment detection script
if [ -f "$SCRIPT_DIR/detect-environment.sh" ]; then
    source "$SCRIPT_DIR/detect-environment.sh"
else
    echo "❌ Error: detect-environment.sh not found in $SCRIPT_DIR"
    exit 1
fi

function_prepare_container_environment_aware() {
    echo "🔧 Starting environment-aware container preparation..."
    
    # Detect environment
    local environment=$(detect_environment)
    echo "🌍 Detected environment: $environment"
    
    # Standard preparation steps (same for all environments)
    echo "📦 Running standard preparation steps..."
    
    if [ -d "/var/www/.composer" ]; then
        chown -R www-data:www-data /var/www/.composer
    fi
    
    # Install composer dependencies
    if command -v composer >/dev/null 2>&1; then
        if [ -f "composer.json" ]; then
            echo "📥 Installing Composer dependencies..."
            sudo -u www-data composer install --no-dev --optimize-autoloader
        fi
    fi
    
    # Create .env file if it doesn't exist
    if ! test -f ".env"; then
        echo "📝 Creating .env file..."
        sudo -u www-data cp .env.example .env
        echo "🔑 Generating application key..."
        sudo -u www-data php artisan key:generate --ansi
    fi
    
    # Environment-specific configuration
    echo "⚙️  Applying environment-specific configuration..."
    
    case "$environment" in
        "production")
            echo "🚀 PRODUCTION: Maximum performance configuration"
            
            # NEVER enable debugging in production
            echo "🔒 Ensuring debugging is DISABLED for production..."
            
            # Update .env for production
            if [ -f ".env" ]; then
                sed -i.bak 's/APP_ENV=.*/APP_ENV=production/' .env
                sed -i.bak 's/APP_DEBUG=.*/APP_DEBUG=false/' .env
                sed -i.bak 's/LOG_LEVEL=.*/LOG_LEVEL=error/' .env
            fi
            
            # Ensure Xdebug is disabled (if config files exist)
            if [ -f "/usr/local/etc/php/conf.d/docker-php-ext-xdebug.ini" ]; then
                sed -i 's/xdebug.mode=.*/xdebug.mode=off/g' /usr/local/etc/php/conf.d/docker-php-ext-xdebug.ini
            fi
            
            echo "✅ Production configuration applied"
            ;;
            
        "staging")
            echo "🧪 STAGING: Performance-optimized with debugging available"
            
            # Default to performance mode, but debugging can be enabled manually
            echo "⚡ Setting performance mode as default..."
            
            # Update .env for staging
            if [ -f ".env" ]; then
                sed -i.bak 's/APP_ENV=.*/APP_ENV=staging/' .env
                sed -i.bak 's/APP_DEBUG=.*/APP_DEBUG=false/' .env
                sed -i.bak 's/LOG_LEVEL=.*/LOG_LEVEL=warning/' .env
            fi
            
            # Disable Xdebug by default (can be enabled via scripts)
            if [ -f "/usr/local/etc/php/conf.d/docker-php-ext-xdebug.ini" ]; then
                sed -i 's/xdebug.mode=.*/xdebug.mode=off/g' /usr/local/etc/php/conf.d/docker-php-ext-xdebug.ini
            fi
            
            echo "✅ Staging configuration applied"
            ;;
            
        "development"|"local")
            echo "🛠️  DEVELOPMENT: Debugging available but performance-first"
            
            # Even in development, default to performance mode
            # Developers can enable debugging when needed
            echo "⚡ Setting performance mode as default (debugging available on-demand)..."
            
            # Update .env for development
            if [ -f ".env" ]; then
                sed -i.bak 's/APP_ENV=.*/APP_ENV=local/' .env
                sed -i.bak 's/APP_DEBUG=.*/APP_DEBUG=false/' .env  # Default to false, enable when needed
                sed -i.bak 's/LOG_LEVEL=.*/LOG_LEVEL=debug/' .env
            fi
            
            # Configure Xdebug for development (but keep it off by default)
            if [ -f "/usr/local/etc/php/conf.d/docker-php-ext-xdebug.ini" ]; then
                # Update Xdebug configuration for modern versions
                sed -i 's/remote_enable=on/mode=off/g' /usr/local/etc/php/conf.d/docker-php-ext-xdebug.ini
                sed -i 's/remote_host/client_host/g' /usr/local/etc/php/conf.d/docker-php-ext-xdebug.ini
                
                # Ensure it's off by default
                sed -i 's/xdebug.mode=.*/xdebug.mode=off/g' /usr/local/etc/php/conf.d/docker-php-ext-xdebug.ini
            fi
            
            echo "✅ Development configuration applied"
            echo "💡 Use ./scripts/development-mode.sh to enable debugging when needed"
            ;;
            
        *)
            echo "⚠️  UNKNOWN ENVIRONMENT: Applying safe defaults..."
            
            # Safe defaults - performance mode
            if [ -f ".env" ]; then
                sed -i.bak 's/APP_DEBUG=.*/APP_DEBUG=false/' .env
            fi
            
            if [ -f "/usr/local/etc/php/conf.d/docker-php-ext-xdebug.ini" ]; then
                sed -i 's/xdebug.mode=.*/xdebug.mode=off/g' /usr/local/etc/php/conf.d/docker-php-ext-xdebug.ini
            fi
            
            echo "✅ Safe default configuration applied"
            ;;
    esac
    
    echo ""
    echo "🎉 Environment-aware container preparation complete!"
    echo "   Environment: $environment"
    echo "   Performance: Optimized"
    echo "   Debugging: $([ "$environment" = "production" ] && echo "Disabled (Security)" || echo "Available on-demand")"
}

# Execute the function
function_prepare_container_environment_aware &

# Wait for completion
wait

echo "✅ Container preparation finished successfully!"
