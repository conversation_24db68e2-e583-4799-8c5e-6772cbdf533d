#!/bin/bash

# Environment Flag Setup Script
# Usage: ./scripts/setup-environment-flags.sh [staging|production]
# Creates environment identification flags on servers

set -e

ENVIRONMENT=${1:-"auto"}

echo "🏷️  Environment Flag Setup"
echo "=========================="

# Auto-detect if not specified
if [ "$ENVIRONMENT" = "auto" ]; then
    local server_ip=$(hostname -I | awk '{print $1}' 2>/dev/null || echo "unknown")
    case "$server_ip" in
        "*************")
            ENVIRONMENT="staging"
            ;;
        "***************")
            ENVIRONMENT="production"
            ;;
        *)
            echo "❌ Cannot auto-detect environment. Please specify: staging or production"
            echo "Usage: $0 [staging|production]"
            exit 1
            ;;
    esac
fi

echo "🌍 Setting up environment: $ENVIRONMENT"
echo "🖥️  Server IP: $(hostname -I | awk '{print $1}' 2>/dev/null || echo 'unknown')"
echo "🏷️  Hostname: $(hostname 2>/dev/null || echo 'unknown')"
echo ""

case "$ENVIRONMENT" in
    "staging")
        echo "🧪 Creating staging environment flags..."
        
        # Create system-level flag
        sudo tee /etc/environment-staging > /dev/null << 'EOF'
# Staging Environment Flag
# Created by setup-environment-flags.sh
ENVIRONMENT=staging
SERVER_TYPE=staging
PERFORMANCE_MODE=required
EOF
        
        # Create project-level flag
        sudo tee /var/www/STAGING > /dev/null << 'EOF'
STAGING ENVIRONMENT
===================
This server is configured for staging/testing purposes.
Performance mode is automatically enabled.
Debugging tools are available but disabled by default.

Server IP: *************
Environment: staging
Performance: optimized
EOF
        
        echo "✅ Staging environment flags created:"
        echo "   - /etc/environment-staging"
        echo "   - /var/www/STAGING"
        ;;
        
    "production")
        echo "🚀 Creating production environment flags..."
        
        # Create system-level flag
        sudo tee /etc/environment-production > /dev/null << 'EOF'
# Production Environment Flag
# Created by setup-environment-flags.sh
ENVIRONMENT=production
SERVER_TYPE=production
PERFORMANCE_MODE=critical
DEBUG_MODE=disabled
EOF
        
        # Create project-level flag
        sudo tee /var/www/PRODUCTION > /dev/null << 'EOF'
PRODUCTION ENVIRONMENT
======================
⚠️  CRITICAL: This server serves live traffic!

Performance mode is MANDATORY.
Debugging tools are DISABLED for security and performance.
Any debugging must be done in staging environment.

Server IP: ***************
Environment: production
Performance: maximum
Security: high
EOF
        
        echo "✅ Production environment flags created:"
        echo "   - /etc/environment-production"
        echo "   - /var/www/PRODUCTION"
        ;;
        
    *)
        echo "❌ Invalid environment: $ENVIRONMENT"
        echo "Valid options: staging, production"
        exit 1
        ;;
esac

echo ""
echo "🔧 Setting appropriate permissions..."
sudo chmod 644 /etc/environment-$ENVIRONMENT
sudo chmod 644 /var/www/$(echo $ENVIRONMENT | tr '[:lower:]' '[:upper:]')

echo ""
echo "✅ Environment flags setup complete!"
echo ""
echo "📋 Verification:"
echo "   Environment: $ENVIRONMENT"
echo "   Flags created: ✅"
echo "   Permissions set: ✅"
echo ""
echo "💡 These flags will help the deployment script automatically"
echo "   detect the environment and apply appropriate configurations."
