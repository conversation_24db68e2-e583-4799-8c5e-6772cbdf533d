#!/bin/bash

# Rebuild the Docker container with performance optimizations
# Usage: ./scripts/rebuild-container.sh

set -e

echo "🔨 Rebuilding Docker container with performance optimizations..."
echo ""
echo "⚠️  This will take a few minutes but ensures clean configuration"
echo ""

# Stop the container
echo "🛑 Stopping containers..."
docker compose down

# Rebuild the container
echo "🔨 Rebuilding container..."
docker compose build rentals

# Start the container
echo "🚀 Starting container..."
docker compose up -d

# Wait for container to be ready
echo "⏳ Waiting for container to be ready..."
sleep 10

# Ensure performance mode is active
echo "🎯 Ensuring performance mode is active..."
./scripts/performance-mode.sh

echo ""
echo "✅ Container rebuilt successfully!"
echo ""
echo "📊 Current status:"
./scripts/check-status.sh
