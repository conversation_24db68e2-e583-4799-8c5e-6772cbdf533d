#!/bin/bash

# Environment Detection Script
# Usage: ./scripts/detect-environment.sh
# Returns: staging, production, or development

set -e

# Function to detect environment
detect_environment() {
    local environment="development"  # Default fallback
    
    # Method 1: Check server IP addresses (most reliable for your setup)
    local server_ip=$(hostname -I | awk '{print $1}' 2>/dev/null || echo "unknown")
    
    case "$server_ip" in
        "*************")
            environment="staging"
            ;;
        "***************")
            environment="production"
            ;;
        *)
            # Method 2: Check hostname patterns
            local hostname=$(hostname 2>/dev/null || echo "unknown")
            case "$hostname" in
                *staging* | *stage*)
                    environment="staging"
                    ;;
                *production* | *prod* | *live*)
                    environment="production"
                    ;;
                *local* | *dev*)
                    environment="development"
                    ;;
                *)
                    # Method 3: Check for environment flag files
                    if [ -f "/etc/environment-production" ] || [ -f "/var/www/PRODUCTION" ]; then
                        environment="production"
                    elif [ -f "/etc/environment-staging" ] || [ -f "/var/www/STAGING" ]; then
                        environment="staging"
                    elif [ -f "/.dockerenv" ] || [ -n "$DOCKER_CONTAINER" ]; then
                        environment="development"
                    else
                        # Method 4: Check Laravel .env file
                        if [ -f ".env" ]; then
                            local app_env=$(grep "APP_ENV=" .env 2>/dev/null | cut -d'=' -f2 | tr -d '"' | tr -d "'" || echo "")
                            if [ -n "$app_env" ]; then
                                environment="$app_env"
                            fi
                        fi
                    fi
                    ;;
            esac
            ;;
    esac
    
    echo "$environment"
}

# Function to validate environment
validate_environment() {
    local env="$1"
    case "$env" in
        "production"|"staging"|"development"|"local")
            return 0
            ;;
        *)
            return 1
            ;;
    esac
}

# Main execution
main() {
    local detected_env=$(detect_environment)
    
    if validate_environment "$detected_env"; then
        echo "$detected_env"
        return 0
    else
        echo "development"  # Safe fallback
        return 1
    fi
}

# Execute if called directly
if [ "${BASH_SOURCE[0]}" = "${0}" ]; then
    main "$@"
fi
