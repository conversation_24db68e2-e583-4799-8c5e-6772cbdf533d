#!/bin/bash

# Check current performance and debugging status (SERVER VERSION)
# Usage: ./scripts/server-check-status.sh

set -e

echo "🔍 Checking current server environment status..."
echo ""

echo "📊 Current Configuration Status:"
echo "================================"

# Check Xdebug status
XDEBUG_MODE=$(php -r "echo ini_get('xdebug.mode');" 2>/dev/null | tr -d '\r\n' || echo "unknown")
if [ "$XDEBUG_MODE" = "" ] || [ "$XDEBUG_MODE" = "off" ]; then
    echo "🟢 Xdebug: DISABLED (performance mode)"
else
    echo "🟡 Xdebug: ENABLED ($XDEBUG_MODE mode)"
fi

# Check PCOV status
PCOV_ENABLED=$(php -r "echo ini_get('pcov.enabled');" 2>/dev/null | tr -d '\r\n' || echo "unknown")
if [ "$PCOV_ENABLED" = "0" ] || [ "$PCOV_ENABLED" = "" ]; then
    echo "🟢 PCOV: DISABLED (performance mode)"
else
    echo "🟡 PCOV: ENABLED (coverage mode)"
fi

# Check APP_DEBUG status
if [ -f ".env" ]; then
    APP_DEBUG=$(grep "APP_DEBUG=" .env 2>/dev/null | cut -d'=' -f2 | tr -d '\r\n' || echo "unknown")
    if [ "$APP_DEBUG" = "false" ]; then
        echo "🟢 Laravel Debugbar: DISABLED (performance mode)"
    else
        echo "🟡 Laravel Debugbar: ENABLED (debug mode)"
    fi
else
    echo "⚠️  .env file not found"
    APP_DEBUG="unknown"
fi

echo ""

# Determine current mode
if [ "$XDEBUG_MODE" = "" ] || [ "$XDEBUG_MODE" = "off" ] && [ "$PCOV_ENABLED" = "0" ] && [ "$APP_DEBUG" = "false" ]; then
    echo "✅ Current Mode: PERFORMANCE MODE (optimal for production)"
    echo ""
    echo "💡 Available actions:"
    echo "   ./scripts/server-development-mode.sh    - Enable debugging tools"
    echo "   ./scripts/server-toggle-coverage.sh on  - Enable coverage for testing"
elif [ "$XDEBUG_MODE" != "" ] && [ "$XDEBUG_MODE" != "off" ] && [ "$APP_DEBUG" = "true" ]; then
    echo "🛠️  Current Mode: DEVELOPMENT MODE (debugging enabled)"
    echo ""
    echo "💡 Available actions:"
    echo "   ./scripts/server-performance-mode.sh     - Return to performance mode"
    echo "   ./scripts/server-toggle-coverage.sh on   - Enable coverage for testing"
elif [ "$PCOV_ENABLED" = "1" ]; then
    echo "🧪 Current Mode: COVERAGE MODE (testing with coverage)"
    echo ""
    echo "💡 Available actions:"
    echo "   ./scripts/server-toggle-coverage.sh off  - Disable coverage"
    echo "   ./scripts/server-performance-mode.sh     - Return to performance mode"
else
    echo "⚠️  Current Mode: MIXED/CUSTOM CONFIGURATION"
    echo ""
    echo "💡 Recommended actions:"
    echo "   ./scripts/server-performance-mode.sh     - Reset to performance mode"
    echo "   ./scripts/server-development-mode.sh     - Enable full debugging"
fi

echo ""

# Performance test
echo "⚡ Quick Performance Test:"
echo "========================="
echo "Testing PHP execution speed..."

PERF_TIME=$(php -r "
\$start = microtime(true);
for(\$i = 0; \$i < 100000; \$i++) { \$x = \$i * 2; }
\$end = microtime(true);
echo number_format((\$end - \$start) * 1000, 2);
" 2>/dev/null | tr -d '\r\n')

echo "PHP execution time: ${PERF_TIME}ms"

if (( $(echo "$PERF_TIME < 10" | bc -l 2>/dev/null || echo "0") )); then
    echo "🟢 PHP Performance: EXCELLENT"
elif (( $(echo "$PERF_TIME < 50" | bc -l 2>/dev/null || echo "0") )); then
    echo "🟡 PHP Performance: GOOD"
else
    echo "🔴 PHP Performance: SLOW (check debugging extensions)"
fi

echo ""

# Show PHP configuration info
echo "🔧 PHP Configuration Info:"
echo "=========================="
echo "PHP Version: $(php -r 'echo PHP_VERSION;')"
echo "PHP SAPI: $(php -r 'echo PHP_SAPI;')"

# Find PHP configuration directory
PHP_INI_DIR=$(php --ini | grep "Scan for additional .ini files" | cut -d: -f2 | xargs)
if [ -z "$PHP_INI_DIR" ] || [ "$PHP_INI_DIR" = "(none)" ]; then
    echo "Additional .ini files: None"
else
    echo "Additional .ini files directory: $PHP_INI_DIR"
fi

# Show loaded extensions
echo ""
echo "📦 Loaded Debug Extensions:"
echo "=========================="
php -m | grep -E "(xdebug|pcov)" || echo "No debug extensions found"

echo ""
echo "📋 Quick Reference (Server Version):"
echo "===================================="
echo "Performance Mode:   ./scripts/server-performance-mode.sh"
echo "Development Mode:   ./scripts/server-development-mode.sh"
echo "Toggle Xdebug:      ./scripts/server-toggle-debug.sh [on|off]"
echo "Toggle Coverage:    ./scripts/server-toggle-coverage.sh [on|off]"
echo "Check Status:       ./scripts/server-check-status.sh"
