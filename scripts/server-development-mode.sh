#!/bin/bash

# Enable development mode with debugging tools (SERVER VERSION)
# Usage: ./scripts/server-development-mode.sh

set -e

echo "🛠️  Enabling development mode (server version)..."
echo "   - Enabling Xdebug debug mode"
echo "   - Enabling Laravel Debugbar"
echo "   - <PERSON>O<PERSON> remains disabled (use coverage scripts for testing)"

# Find PHP configuration directory
PHP_INI_DIR=$(php --ini | grep "Scan for additional .ini files" | cut -d: -f2 | xargs)
if [ -z "$PHP_INI_DIR" ] || [ "$PHP_INI_DIR" = "(none)" ]; then
    PHP_INI_DIR="/etc/php/$(php -r 'echo PHP_MAJOR_VERSION.".".PHP_MINOR_VERSION;')/apache2/conf.d"
    if [ ! -d "$PHP_INI_DIR" ]; then
        PHP_INI_DIR="/etc/php/$(php -r 'echo PHP_MAJOR_VERSION.".".PHP_MINOR_VERSION;')/fpm/conf.d"
    fi
    if [ ! -d "$PHP_INI_DIR" ]; then
        PHP_INI_DIR="/usr/local/etc/php/conf.d"
    fi
fi

echo "📁 Using PHP config directory: $PHP_INI_DIR"

# Create development configuration files
echo "🔧 Creating development configuration files..."

# Enable Xdebug debug mode
sudo tee "$PHP_INI_DIR/99-xdebug-performance.ini" > /dev/null << 'EOF'
; Development mode - Xdebug enabled
xdebug.mode=debug
xdebug.client_host=127.0.0.1
xdebug.client_port=9003
xdebug.start_with_request=yes
EOF

# Keep PCOV disabled (use separate script for coverage)
sudo tee "$PHP_INI_DIR/99-pcov-performance.ini" > /dev/null << 'EOF'
; Development mode - PCOV disabled (use coverage scripts when needed)
pcov.enabled=0
EOF

# Enable Laravel Debugbar by setting APP_DEBUG=true
if [ -f ".env" ]; then
    echo "🔧 Enabling Laravel Debugbar..."
    sed -i.bak 's/APP_DEBUG=false/APP_DEBUG=true/' .env
fi

# Restart web server
echo "🔄 Restarting web server to apply changes..."
if systemctl is-active --quiet apache2; then
    sudo systemctl reload apache2
elif systemctl is-active --quiet nginx; then
    sudo systemctl reload nginx
    if systemctl is-active --quiet php*-fpm; then
        sudo systemctl reload php*-fpm
    fi
elif command -v service >/dev/null 2>&1; then
    # Fallback for older systems
    sudo service apache2 reload 2>/dev/null || sudo service nginx reload 2>/dev/null || true
    sudo service php*-fpm reload 2>/dev/null || true
else
    echo "⚠️  Could not automatically restart web server. Please restart manually."
fi

echo "✅ Development mode enabled (server version)!"
echo ""
echo "📊 Current status:"
echo "   Xdebug mode: debug"
echo "   PCOV coverage: disabled (use coverage scripts when needed)"
echo "   Laravel Debugbar: enabled"
echo ""
echo "💡 To return to performance mode: ./scripts/server-performance-mode.sh"
echo "💡 To enable coverage for testing: ./scripts/server-toggle-coverage.sh on"
echo "💡 To check status: ./scripts/server-check-status.sh"
echo ""
echo "🚨 Note: Development mode will slow down the application"
echo "   Use performance mode for normal development work"
