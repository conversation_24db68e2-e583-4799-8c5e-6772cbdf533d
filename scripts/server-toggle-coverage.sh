#!/bin/bash

# Toggle PCOV coverage mode on/off for performance (SERVER VERSION)
# Usage: ./scripts/server-toggle-coverage.sh [on|off]

set -e

MODE=${1:-"off"}

if [ "$MODE" != "on" ] && [ "$MODE" != "off" ]; then
    echo "Usage: $0 [on|off]"
    echo "  on  - Enable PCOV coverage mode"
    echo "  off - Disable PCOV coverage mode (default)"
    exit 1
fi

echo "🔧 Toggling PCOV coverage: $MODE (server version)"

# Find PHP configuration directory
PHP_INI_DIR=$(php --ini | grep "Scan for additional .ini files" | cut -d: -f2 | xargs)
if [ -z "$PHP_INI_DIR" ] || [ "$PHP_INI_DIR" = "(none)" ]; then
    PHP_INI_DIR="/etc/php/$(php -r 'echo PHP_MAJOR_VERSION.".".PHP_MINOR_VERSION;')/apache2/conf.d"
    if [ ! -d "$PHP_INI_DIR" ]; then
        PHP_INI_DIR="/etc/php/$(php -r 'echo PHP_MAJOR_VERSION.".".PHP_MINOR_VERSION;')/fpm/conf.d"
    fi
    if [ ! -d "$PHP_INI_DIR" ]; then
        PHP_INI_DIR="/usr/local/etc/php/conf.d"
    fi
fi

echo "📁 Using PHP config directory: $PHP_INI_DIR"

if [ "$MODE" = "on" ]; then
    echo "⚠️  Enabling PCOV coverage (will slow down the application)"
    sudo tee "$PHP_INI_DIR/99-pcov-performance.ini" > /dev/null << 'EOF'
; PCOV enabled for coverage
pcov.enabled=1
pcov.directory=/var/www/html
EOF
else
    echo "✅ Disabling PCOV coverage (for better performance)"
    sudo tee "$PHP_INI_DIR/99-pcov-performance.ini" > /dev/null << 'EOF'
; PCOV disabled for performance
pcov.enabled=0
EOF
fi

# Restart web server
echo "🔄 Restarting web server to apply changes..."
if systemctl is-active --quiet apache2; then
    sudo systemctl reload apache2
elif systemctl is-active --quiet nginx; then
    sudo systemctl reload nginx
    if systemctl is-active --quiet php*-fpm; then
        sudo systemctl reload php*-fpm
    fi
elif command -v service >/dev/null 2>&1; then
    # Fallback for older systems
    sudo service apache2 reload 2>/dev/null || sudo service nginx reload 2>/dev/null || true
    sudo service php*-fpm reload 2>/dev/null || true
else
    echo "⚠️  Could not automatically restart web server. Please restart manually."
fi

echo "✅ PCOV coverage is now: $MODE"

# Show current status
echo ""
echo "📊 Current PHP extensions status:"
php -m | grep -E "(xdebug|pcov)" || echo "No debug extensions found"
